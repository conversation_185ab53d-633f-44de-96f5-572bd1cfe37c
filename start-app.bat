@echo off
setlocal enabledelayedexpansion

:: Retro Notes - Full Stack Startup Script (Windows)
:: This script starts both Flask backend and Next.js frontend

echo.
echo ========================================
echo    🎵 RETRO NOTES - STARTUP SCRIPT 🎵
echo ========================================
echo.

:: Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

:: Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: Python is not installed or not in PATH
    echo Please install Python from https://python.org/
    pause
    exit /b 1
)

:: Check if pip is installed
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: pip is not installed or not in PATH
    echo Please install pip or ensure Python installation includes pip
    pause
    exit /b 1
)

echo ✅ Prerequisites check passed
echo.

:: Check if backend directory exists
if not exist "note-app-be" (
    echo ❌ ERROR: Backend directory 'note-app-be' not found
    echo Please ensure you're running this script from the project root directory
    pause
    exit /b 1
)

:: Check if frontend directory exists
if not exist "note-app-frontend" (
    echo ❌ ERROR: Frontend directory 'note-app-frontend' not found
    echo Please ensure you're running this script from the project root directory
    pause
    exit /b 1
)

echo 📁 Project directories found
echo.

:: Install backend dependencies if needed
echo 🔧 Checking backend dependencies...
if not exist "note-app-be\venv" (
    echo Creating Python virtual environment...
    cd note-app-be
    python -m venv venv
    if errorlevel 1 (
        echo ❌ ERROR: Failed to create virtual environment
        cd ..
        pause
        exit /b 1
    )
    cd ..
)

:: Activate virtual environment and install dependencies
echo Installing/updating backend dependencies...
cd note-app-be
call venv\Scripts\activate.bat
pip install -r requirements.txt >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: Failed to install backend dependencies
    echo Please check requirements.txt and try running: pip install -r requirements.txt
    deactivate
    cd ..
    pause
    exit /b 1
)
cd ..

:: Install frontend dependencies if needed
echo 🔧 Checking frontend dependencies...
if not exist "note-app-frontend\node_modules" (
    echo Installing frontend dependencies...
    cd note-app-frontend
    npm install
    if errorlevel 1 (
        echo ❌ ERROR: Failed to install frontend dependencies
        echo Please check package.json and try running: npm install
        cd ..
        pause
        exit /b 1
    )
    cd ..
) else (
    echo Frontend dependencies already installed
)

echo.
echo ✅ All dependencies are ready
echo.

:: Create log directory
if not exist "logs" mkdir logs

:: Start Flask backend
echo 🚀 Starting Flask backend server...
cd note-app-be
start "Flask Backend - Retro Notes" cmd /k "call venv\Scripts\activate.bat && echo Backend server starting on http://localhost:5000 && python app.py"
cd ..

:: Wait a moment for backend to start
echo ⏳ Waiting for backend to initialize...
timeout /t 3 /nobreak >nul

:: Start Next.js frontend
echo 🚀 Starting Next.js frontend server...
cd note-app-frontend
start "Next.js Frontend - Retro Notes" cmd /k "echo Frontend server starting on http://localhost:3000 && npm run dev"
cd ..

echo.
echo ========================================
echo           🎉 STARTUP COMPLETE! 🎉
echo ========================================
echo.
echo 📍 Backend:  http://localhost:5000
echo 📍 Frontend: http://localhost:3000
echo.
echo 💡 Both servers are running in separate windows
echo 💡 Close those windows or press Ctrl+C to stop the servers
echo 💡 The frontend will automatically open in your browser
echo.
echo ⚠️  If you encounter any issues:
echo    1. Check that ports 3000 and 5000 are available
echo    2. Ensure all dependencies are properly installed
echo    3. Check the server windows for error messages
echo    4. Refer to README.md for troubleshooting
echo.

:: Wait for user input before closing
echo Press any key to close this window (servers will continue running)...
pause >nul
