# 🛠️ Development Guide - Retro Notes

This guide provides detailed information for developers working on the Retro Notes application.

## 🏗️ Architecture Overview

### **Technology Stack**

#### **Backend (Flask)**
- **Framework**: Flask 2.0+ with Python 3.8+
- **Database**: SQLite (development) / MySQL (production)
- **ORM**: SQLAlchemy
- **API**: RESTful JSON API
- **Environment**: Python virtual environment

#### **Frontend (Next.js)**
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with custom retro theme
- **State Management**: Zustand
- **Icons**: Lucide React
- **Build Tool**: Next.js built-in bundler

### **Application Flow**
```
User Browser ↔ Next.js Frontend (Port 3000) ↔ Flask Backend (Port 5000) ↔ Database
```

## 🚀 Development Setup

### **Initial Setup**
1. Clone the repository
2. Run the startup script (`start-app.bat` or `start-app.sh`)
3. Both servers will start automatically

### **Manual Development Setup**

#### **Backend Development**
```bash
cd note-app-be

# Create and activate virtual environment
python -m venv venv
source venv/bin/activate  # Linux/macOS
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Set environment variables
export FLASK_ENV=development
export DEBUG=True

# Start development server
python app.py
```

#### **Frontend Development**
```bash
cd note-app-frontend

# Install dependencies
npm install

# Start development server
npm run dev

# Or build for production
npm run build
npm run start
```

## 📁 Code Organization

### **Backend Structure**
```
note-app-be/
├── app.py              # Main Flask application and routes
├── models.py           # SQLAlchemy database models
├── config.py           # Configuration classes
├── requirements.txt    # Python dependencies
├── .env               # Environment variables (create this)
└── venv/              # Virtual environment
```

### **Frontend Structure**
```
note-app-frontend/src/
├── app/                    # Next.js App Router
│   ├── layout.tsx         # Root layout with theme
│   ├── page.tsx           # Home page (notes list)
│   └── notes/             # Note-related pages
│       ├── create/        # Create note page
│       └── [id]/          # Dynamic routes
├── components/            # Reusable components
│   ├── Layout/           # Header, navigation
│   ├── NoteCard.tsx      # Individual note display
│   ├── NoteForm.tsx      # Create/edit forms
│   ├── SearchBar.tsx     # Search functionality
│   └── ...               # Other UI components
├── lib/                  # Core utilities
│   ├── api.ts           # API client for Flask
│   ├── store.ts         # Zustand state management
│   ├── types.ts         # TypeScript definitions
│   └── utils.ts         # Helper functions
└── styles/              # Global styles
    └── globals.css      # Retro theme CSS
```

## 🎨 Design System

### **Color Palette**
```css
/* Light Theme */
--retro-cream: #F5F5DC;
--retro-beige: #F5E6D3;
--retro-orange: #FF8C42;
--retro-brown: #8B4513;
--retro-green: #9CAF88;
--retro-paper: #FDF6E3;

/* Dark Theme */
--retro-dark-bg: #2C1810;
--retro-dark-text: #F5E6D3;
--retro-dark-accent: #FF8C42;
```

### **Typography**
- **Headers**: Crimson Text (serif)
- **Body**: Courier Prime (monospace)
- **UI Elements**: System fonts with fallbacks

### **Component Classes**
```css
.retro-button      /* Vintage-styled buttons */
.retro-input       /* Form inputs with retro styling */
.retro-card        /* Paper-like content containers */
.retro-heading     /* Styled headings */
.retro-text        /* Body text with retro font */
```

## 🔌 API Development

### **Adding New Endpoints**

1. **Define the route in `app.py`:**
```python
@app.route('/notes/search', methods=['GET'])
def search_notes():
    query = request.args.get('q', '')
    notes = Note.query.filter(
        Note.title.contains(query) | 
        Note.content.contains(query)
    ).all()
    return jsonify([note.to_dict() for note in notes])
```

2. **Update the API client in `frontend/src/lib/api.ts`:**
```typescript
async searchNotes(query: string): Promise<Note[]> {
  return this.request<Note[]>(`/notes/search?q=${encodeURIComponent(query)}`);
}
```

3. **Add to Zustand store in `frontend/src/lib/store.ts`:**
```typescript
searchNotes: async (query: string) => {
  set({ isLoading: true });
  try {
    const notes = await apiClient.searchNotes(query);
    set({ searchResults: notes, isLoading: false });
  } catch (error) {
    set({ error: error.message, isLoading: false });
  }
}
```

### **Error Handling Patterns**

#### **Backend Error Responses**
```python
try:
    # Operation here
    return jsonify(result), 200
except Exception as e:
    return jsonify({'error': str(e)}), 500
```

#### **Frontend Error Handling**
```typescript
try {
  const result = await apiClient.someOperation();
  // Handle success
} catch (error) {
  setError(error instanceof Error ? error.message : 'Unknown error');
}
```

## 🧩 Component Development

### **Creating New Components**

1. **Create component file:**
```typescript
// src/components/NewComponent.tsx
'use client';

import { useState } from 'react';
import { cn } from '@/lib/utils';

interface NewComponentProps {
  title: string;
  onAction?: () => void;
}

export function NewComponent({ title, onAction }: NewComponentProps) {
  const [isActive, setIsActive] = useState(false);

  return (
    <div className={cn(
      'retro-card',
      isActive && 'bg-retro-orange/20'
    )}>
      <h3 className="retro-heading">{title}</h3>
      <button 
        onClick={onAction}
        className="retro-button"
      >
        Action
      </button>
    </div>
  );
}
```

2. **Export from index (if using barrel exports):**
```typescript
// src/components/index.ts
export { NewComponent } from './NewComponent';
```

### **Styling Guidelines**

1. **Use Tailwind classes with retro theme:**
```typescript
className="bg-retro-paper text-retro-brown border-retro-brown-light"
```

2. **Use utility function for conditional classes:**
```typescript
className={cn(
  'base-classes',
  condition && 'conditional-classes',
  variant === 'primary' && 'primary-classes'
)}
```

3. **Follow responsive design patterns:**
```typescript
className="text-sm md:text-base lg:text-lg"
```

## 🗄️ Database Development

### **Model Changes**

1. **Update model in `models.py`:**
```python
class Note(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    content = db.Column(db.Text, nullable=True)
    category = db.Column(db.String(100), nullable=True)  # New field
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_at = db.Column(db.DateTime, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())
    
    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'category': self.category,  # Include new field
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
```

2. **Update TypeScript types:**
```typescript
// src/lib/types.ts
export interface Note {
  id: number;
  title: string;
  content: string;
  category?: string;  // New field
  created_at: string;
  updated_at: string;
}
```

3. **For development, delete existing database and restart:**
```bash
rm note-app-be/instance/database.db  # If using SQLite
python app.py  # Tables will be recreated
```

## 🧪 Testing Strategies

### **Manual Testing Checklist**

#### **Backend API Testing**
```bash
# Test all endpoints
curl -X GET http://localhost:5000/notes
curl -X POST http://localhost:5000/notes -H "Content-Type: application/json" -d '{"title":"Test","content":"Content"}'
curl -X GET http://localhost:5000/notes/1
curl -X PUT http://localhost:5000/notes/1 -H "Content-Type: application/json" -d '{"title":"Updated"}'
curl -X DELETE http://localhost:5000/notes/1
```

#### **Frontend Testing**
- [ ] Create note form validation
- [ ] Edit note with existing data
- [ ] Delete confirmation dialog
- [ ] Search functionality
- [ ] Sort options
- [ ] Theme toggle
- [ ] Responsive design
- [ ] Error states
- [ ] Loading states

### **Automated Testing (Future)**

#### **Backend Tests**
```python
# tests/test_api.py
import unittest
from app import create_app

class TestNotesAPI(unittest.TestCase):
    def setUp(self):
        self.app = create_app('testing')
        self.client = self.app.test_client()
    
    def test_get_notes(self):
        response = self.client.get('/notes')
        self.assertEqual(response.status_code, 200)
```

#### **Frontend Tests**
```typescript
// __tests__/components/NoteCard.test.tsx
import { render, screen } from '@testing-library/react';
import { NoteCard } from '@/components/NoteCard';

test('renders note card with title', () => {
  const note = { id: 1, title: 'Test Note', content: 'Content' };
  render(<NoteCard note={note} />);
  expect(screen.getByText('Test Note')).toBeInTheDocument();
});
```

## 🚀 Deployment

### **Production Configuration**

#### **Backend Production Settings**
```python
# config.py
class ProductionConfig(Config):
    DEBUG = False
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')
    SECRET_KEY = os.environ.get('SECRET_KEY')
```

#### **Frontend Production Build**
```bash
cd note-app-frontend
npm run build
npm run start
```

### **Environment Variables**

#### **Backend (.env)**
```env
FLASK_ENV=production
DEBUG=False
SECRET_KEY=your-production-secret-key
DATABASE_URL=your-production-database-url
```

#### **Frontend (.env.local)**
```env
NEXT_PUBLIC_API_URL=https://your-api-domain.com
```

## 🔧 Debugging

### **Backend Debugging**
```python
# Add debug prints
print(f"Debug: {variable}")

# Use Flask debug mode
app.run(debug=True)

# Check logs
tail -f logs/backend.log
```

### **Frontend Debugging**
```typescript
// Console logging
console.log('Debug:', variable);

// React DevTools
// Install browser extension

// Check network requests
// Use browser DevTools Network tab
```

### **Common Issues**

1. **CORS Errors**: Check Flask CORS configuration
2. **Port Conflicts**: Use different ports or kill existing processes
3. **Database Errors**: Check connection string and permissions
4. **Build Errors**: Clear node_modules and reinstall
5. **Import Errors**: Check file paths and exports

## 📈 Performance Optimization

### **Backend Optimization**
- Use database indexing for search queries
- Implement pagination for large datasets
- Add caching for frequently accessed data
- Optimize database queries

### **Frontend Optimization**
- Use Next.js Image component for images
- Implement lazy loading for components
- Optimize bundle size with code splitting
- Use React.memo for expensive components

## 🤝 Contributing Guidelines

### **Code Style**
- **Python**: Follow PEP 8 guidelines
- **TypeScript**: Use Prettier and ESLint
- **CSS**: Use Tailwind classes, avoid custom CSS when possible

### **Git Workflow**
1. Create feature branch: `git checkout -b feature/new-feature`
2. Make changes and test thoroughly
3. Commit with descriptive messages
4. Push and create pull request
5. Code review and merge

### **Pull Request Template**
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Manual testing completed
- [ ] All existing tests pass
- [ ] New tests added (if applicable)

## Screenshots (if applicable)
Add screenshots for UI changes
```

## 📚 Resources

### **Documentation**
- [Flask Documentation](https://flask.palletsprojects.com/)
- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)

### **Tools**
- [Postman](https://www.postman.com/) - API testing
- [VS Code](https://code.visualstudio.com/) - IDE
- [React DevTools](https://react.dev/learn/react-developer-tools) - React debugging
- [SQLite Browser](https://sqlitebrowser.org/) - Database inspection

---

Happy coding! 🚀✨
