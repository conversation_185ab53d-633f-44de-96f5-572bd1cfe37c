# 🚀 Retro Notes - Quick Start

## C<PERSON>ch chạy ứng dụng

### 1. <PERSON><PERSON>i đặt yêu cầu
- **Node.js 18+** - [Tải tại đây](https://nodejs.org/)
- **Python 3.8+** - [Tải tại đây](https://python.org/)

### 2. Chạy ứng dụng

**Windows:**
```cmd
start.bat
```

**Linux/macOS:**
```bash
./start.sh
```

**Hoặc dùng npm:**
```bash
npm run start
```

### 3. Truy cập ứng dụng
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000

### 4. Dừng ứng dụng
Nhấn `Ctrl+C` trong terminal để dừng cả 2 server.

## Tính năng chính

✅ **T<PERSON><PERSON>, đ<PERSON><PERSON>, sửa, x<PERSON>a ghi chú**  
✅ **Tìm kiếm và sắp xếp**  
✅ **<PERSON><PERSON><PERSON> diện retro/vintage**  
✅ **Responsive design**  
✅ **Dark/Light theme**  
✅ **Keyboard shortcuts**  

## Troubleshooting

**Lỗi port đã được sử dụng:**
```bash
# Windows
netstat -ano | findstr :3000
netstat -ano | findstr :5000
taskkill /PID <PID> /F

# Linux/macOS  
lsof -ti:3000 | xargs kill -9
lsof -ti:5000 | xargs kill -9
```

**Lỗi dependencies:**
```bash
# Xóa và cài lại
rm -rf note-app-frontend/node_modules
rm -rf note-app-be/venv
# Chạy lại start script
```

Vậy là xong! Enjoy your retro notes! 📝✨
