#!/bin/bash

# Retro Notes - Shutdown Script (Unix/Linux/macOS)
# This script stops both Flask backend and Next.js frontend servers

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

echo
echo "========================================"
echo "   🛑 RETRO NOTES - SHUTDOWN SCRIPT 🛑"
echo "========================================"
echo

print_status "🔍 Looking for running servers..."

# Kill processes on port 5000 (Flask backend)
BACKEND_PIDS=$(lsof -ti:5000 2>/dev/null)
if [ ! -z "$BACKEND_PIDS" ]; then
    print_status "Stopping Flask backend server..."
    echo $BACKEND_PIDS | xargs kill -TERM 2>/dev/null
    sleep 2
    # Force kill if still running
    echo $BACKEND_PIDS | xargs kill -9 2>/dev/null
    print_success "Flask backend stopped"
else
    print_status "No Flask backend server found on port 5000"
fi

# Kill processes on port 3000 (Next.js frontend)
FRONTEND_PIDS=$(lsof -ti:3000 2>/dev/null)
if [ ! -z "$FRONTEND_PIDS" ]; then
    print_status "Stopping Next.js frontend server..."
    echo $FRONTEND_PIDS | xargs kill -TERM 2>/dev/null
    sleep 2
    # Force kill if still running
    echo $FRONTEND_PIDS | xargs kill -9 2>/dev/null
    print_success "Next.js frontend stopped"
else
    print_status "No Next.js frontend server found on port 3000"
fi

# Kill any remaining node or python processes related to our app
pkill -f "python.*app.py" 2>/dev/null || true
pkill -f "node.*next.*dev" 2>/dev/null || true

echo
print_success "All Retro Notes servers have been stopped"
echo

# Clean up log files (optional)
read -p "Do you want to clear log files? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    rm -f logs/*.log 2>/dev/null
    print_success "Log files cleared"
fi

echo "Goodbye! 👋"
