@echo off
echo.
echo ========================================
echo    🎵 RETRO NOTES - STARTING... 🎵
echo ========================================
echo.

:: Check prerequisites
echo 🔍 Checking prerequisites...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js not found. Please install Node.js
    pause
    exit /b 1
)

python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found. Please install Python
    pause
    exit /b 1
)

echo ✅ Prerequisites OK
echo.

:: Install backend dependencies
echo 🔧 Setting up backend...
if not exist "note-app-be\venv" (
    echo Creating virtual environment...
    cd note-app-be
    python -m venv venv
    cd ..
)

cd note-app-be
call venv\Scripts\activate.bat
pip install -r requirements.txt >nul 2>&1
echo ✅ Backend ready
cd ..

:: Install frontend dependencies  
echo 🔧 Setting up frontend...
cd note-app-frontend
if not exist "node_modules" (
    echo Installing dependencies...
    npm install >nul 2>&1
)
echo ✅ Frontend ready
cd ..

echo.
echo 🚀 Starting servers...
echo.
echo 📍 Backend:  http://localhost:5000
echo 📍 Frontend: http://localhost:3000
echo.
echo Press Ctrl+C to stop both servers
echo.

:: Start backend in background
cd note-app-be
start /B cmd /c "call venv\Scripts\activate.bat && python app.py"
cd ..

:: Wait a moment
timeout /t 3 /nobreak >nul

:: Start frontend (this will keep the terminal open)
cd note-app-frontend
npm run dev
