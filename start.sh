#!/bin/bash

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

echo
echo "========================================"
echo "   🎵 RETRO NOTES - STARTING... 🎵"
echo "========================================"
echo

# Check prerequisites
echo -e "${BLUE}🔍 Checking prerequisites...${NC}"

if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js not found. Please install Node.js${NC}"
    exit 1
fi

if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo -e "${RED}❌ Python not found. Please install Python${NC}"
    exit 1
fi

# Determine Python command
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
    PIP_CMD="pip3"
else
    PYTHON_CMD="python"
    PIP_CMD="pip"
fi

echo -e "${GREEN}✅ Prerequisites OK${NC}"
echo

# Setup backend
echo -e "${BLUE}🔧 Setting up backend...${NC}"
cd note-app-be

if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    $PYTHON_CMD -m venv venv
fi

source venv/bin/activate
$PIP_CMD install -r requirements.txt > /dev/null 2>&1
echo -e "${GREEN}✅ Backend ready${NC}"
cd ..

# Setup frontend
echo -e "${BLUE}🔧 Setting up frontend...${NC}"
cd note-app-frontend

if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install > /dev/null 2>&1
fi

echo -e "${GREEN}✅ Frontend ready${NC}"
cd ..

echo
echo -e "${BLUE}🚀 Starting servers...${NC}"
echo
echo -e "${GREEN}📍 Backend:  http://localhost:5000${NC}"
echo -e "${GREEN}📍 Frontend: http://localhost:3000${NC}"
echo
echo "Press Ctrl+C to stop both servers"
echo

# Cleanup function
cleanup() {
    echo
    echo -e "${BLUE}🛑 Stopping servers...${NC}"
    
    # Kill processes on ports
    lsof -ti:5000 | xargs kill -9 2>/dev/null || true
    lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    
    echo -e "${GREEN}✅ Servers stopped${NC}"
    exit 0
}

# Set trap for cleanup
trap cleanup SIGINT SIGTERM

# Start backend in background
cd note-app-be
source venv/bin/activate
$PYTHON_CMD app.py &
BACKEND_PID=$!
cd ..

# Wait a moment
sleep 3

# Start frontend (this will keep the terminal open)
cd note-app-frontend
npm run dev
