#!/bin/bash

# Retro Notes - Full Stack Startup Script (Unix/Linux/macOS)
# This script starts both Flask backend and Next.js frontend

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ ERROR: $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${CYAN}💡 $1${NC}"
}

# Cleanup function for graceful shutdown
cleanup() {
    print_status "\n🛑 Shutting down servers..."
    
    # Kill background processes
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        print_status "Backend server stopped"
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        print_status "Frontend server stopped"
    fi
    
    # Kill any remaining processes on our ports
    lsof -ti:5000 | xargs kill -9 2>/dev/null || true
    lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    
    print_success "Cleanup complete. Goodbye! 👋"
    exit 0
}

# Set up signal handlers for graceful shutdown
trap cleanup SIGINT SIGTERM

echo
echo "========================================"
echo "   🎵 RETRO NOTES - STARTUP SCRIPT 🎵"
echo "========================================"
echo

# Check prerequisites
print_status "🔍 Checking prerequisites..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed or not in PATH"
    echo "Please install npm (usually comes with Node.js)"
    exit 1
fi

# Check if Python is installed
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    print_error "Python is not installed or not in PATH"
    echo "Please install Python from https://python.org/"
    exit 1
fi

# Determine Python command
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
    PIP_CMD="pip3"
else
    PYTHON_CMD="python"
    PIP_CMD="pip"
fi

# Check if pip is installed
if ! command -v $PIP_CMD &> /dev/null; then
    print_error "pip is not installed or not in PATH"
    echo "Please install pip or ensure Python installation includes pip"
    exit 1
fi

print_success "Prerequisites check passed"
echo

# Check project structure
print_status "📁 Checking project structure..."

if [ ! -d "note-app-be" ]; then
    print_error "Backend directory 'note-app-be' not found"
    echo "Please ensure you're running this script from the project root directory"
    exit 1
fi

if [ ! -d "note-app-frontend" ]; then
    print_error "Frontend directory 'note-app-frontend' not found"
    echo "Please ensure you're running this script from the project root directory"
    exit 1
fi

print_success "Project directories found"
echo

# Setup backend
print_status "🔧 Setting up backend..."

cd note-app-be

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    print_status "Creating Python virtual environment..."
    $PYTHON_CMD -m venv venv
    if [ $? -ne 0 ]; then
        print_error "Failed to create virtual environment"
        exit 1
    fi
fi

# Activate virtual environment
print_status "Activating virtual environment..."
source venv/bin/activate

# Install/update dependencies
print_status "Installing/updating backend dependencies..."
$PIP_CMD install -r requirements.txt > /dev/null 2>&1
if [ $? -ne 0 ]; then
    print_error "Failed to install backend dependencies"
    echo "Please check requirements.txt and try running: $PIP_CMD install -r requirements.txt"
    exit 1
fi

cd ..

# Setup frontend
print_status "🔧 Setting up frontend..."

cd note-app-frontend

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    print_status "Installing frontend dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        print_error "Failed to install frontend dependencies"
        echo "Please check package.json and try running: npm install"
        exit 1
    fi
else
    print_success "Frontend dependencies already installed"
fi

cd ..

print_success "All dependencies are ready"
echo

# Create logs directory
mkdir -p logs

# Start backend server
print_status "🚀 Starting Flask backend server..."
cd note-app-be
source venv/bin/activate

# Start backend in background and capture PID
nohup $PYTHON_CMD app.py > ../logs/backend.log 2>&1 &
BACKEND_PID=$!

cd ..

# Wait for backend to start
print_status "⏳ Waiting for backend to initialize..."
sleep 3

# Check if backend is running
if ! kill -0 $BACKEND_PID 2>/dev/null; then
    print_error "Backend failed to start. Check logs/backend.log for details"
    exit 1
fi

# Verify backend is responding
for i in {1..10}; do
    if curl -s http://localhost:5000/notes > /dev/null 2>&1; then
        print_success "Backend server is running on http://localhost:5000"
        break
    fi
    if [ $i -eq 10 ]; then
        print_error "Backend server is not responding after 10 attempts"
        cleanup
        exit 1
    fi
    sleep 1
done

# Start frontend server
print_status "🚀 Starting Next.js frontend server..."
cd note-app-frontend

# Start frontend in background and capture PID
nohup npm run dev > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!

cd ..

# Wait for frontend to start
print_status "⏳ Waiting for frontend to initialize..."
sleep 5

# Check if frontend is running
if ! kill -0 $FRONTEND_PID 2>/dev/null; then
    print_error "Frontend failed to start. Check logs/frontend.log for details"
    cleanup
    exit 1
fi

echo
echo "========================================"
echo "          🎉 STARTUP COMPLETE! 🎉"
echo "========================================"
echo
print_info "📍 Backend:  http://localhost:5000"
print_info "📍 Frontend: http://localhost:3000"
echo
print_info "Both servers are running in the background"
print_info "Press Ctrl+C to stop both servers gracefully"
print_info "Logs are available in the 'logs' directory"
echo
print_warning "If you encounter any issues:"
echo "   1. Check that ports 3000 and 5000 are available"
echo "   2. Ensure all dependencies are properly installed"
echo "   3. Check logs/backend.log and logs/frontend.log for errors"
echo "   4. Refer to README.md for troubleshooting"
echo

# Try to open browser (optional)
if command -v xdg-open &> /dev/null; then
    sleep 2
    xdg-open http://localhost:3000 &> /dev/null &
elif command -v open &> /dev/null; then
    sleep 2
    open http://localhost:3000 &> /dev/null &
fi

# Keep script running and wait for interrupt
print_status "🔄 Servers are running. Press Ctrl+C to stop..."
while true; do
    # Check if processes are still running
    if ! kill -0 $BACKEND_PID 2>/dev/null; then
        print_error "Backend process died unexpectedly"
        cleanup
        exit 1
    fi
    
    if ! kill -0 $FRONTEND_PID 2>/dev/null; then
        print_error "Frontend process died unexpectedly"
        cleanup
        exit 1
    fi
    
    sleep 5
done
