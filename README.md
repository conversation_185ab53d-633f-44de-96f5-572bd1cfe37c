# 🎵 Retro Notes - Full Stack Note-Taking Application

A vintage-styled note-taking application that combines the charm of retro aesthetics with modern web technologies. Built with Flask (Python) backend and Next.js 15 (React/TypeScript) frontend.

![Retro Notes Preview](https://img.shields.io/badge/Status-Production%20Ready-brightgreen)
![Python](https://img.shields.io/badge/Python-3.8+-blue)
![Node.js](https://img.shields.io/badge/Node.js-18+-green)
![Next.js](https://img.shields.io/badge/Next.js-15-black)
![Flask](https://img.shields.io/badge/Flask-2.0+-red)

## ✨ Features

### 🎨 **Retro Design System**
- **Vintage Color Palette**: Warm oranges, browns, creams, and muted greens
- **Classic Typography**: Courier Prime and Crimson Text fonts
- **Paper Textures**: Authentic vintage backgrounds and textures
- **Retro UI Elements**: Rounded corners, drop shadows, and vintage buttons
- **Theme Toggle**: Dark/light modes both maintaining retro aesthetics
- **Smooth Animations**: Typewriter effects and smooth transitions

### 📝 **Core Functionality**
- **Complete CRUD Operations**: Create, read, update, and delete notes
- **Rich Text Support**: Full content editing with proper formatting
- **Real-time Search**: Search across note titles and content
- **Advanced Sorting**: Sort by title, creation date, or last updated
- **Responsive Design**: Optimized for desktop, tablet, and mobile
- **Auto-save**: Draft implementation for seamless editing experience

### 🚀 **Technical Features**
- **Modern Stack**: Next.js 15 with App Router and TypeScript
- **State Management**: Zustand for efficient state handling
- **Error Handling**: Comprehensive error boundaries and user feedback
- **Loading States**: Smooth loading indicators for all operations
- **Keyboard Shortcuts**: Ctrl+S to save, Escape to cancel
- **API Integration**: RESTful API communication with Flask backend

## 📋 Prerequisites

Before you begin, ensure you have the following installed on your system:

### **Required Software**
- **Python 3.8+** - [Download from python.org](https://python.org/downloads/)
- **Node.js 18+** - [Download from nodejs.org](https://nodejs.org/downloads/)
- **npm** (comes with Node.js) or **yarn**
- **Git** - [Download from git-scm.com](https://git-scm.com/downloads/)

### **System Requirements**
- **Operating System**: Windows 10+, macOS 10.15+, or Linux (Ubuntu 18.04+)
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 500MB free space for dependencies
- **Browser**: Chrome 88+, Firefox 85+, Safari 14+, or Edge 88+

### **Optional Tools**
- **Python Virtual Environment**: Recommended for dependency isolation
- **VS Code**: Recommended IDE with Python and TypeScript extensions

## 🚀 Quick Start

### **One-Command Startup**

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd note-app
   ```

2. **Run the startup script:**

   **Windows:**
   ```cmd
   start.bat
   ```

   **Linux/macOS:**
   ```bash
   ./start.sh
   ```

   **Cross-platform:**
   ```bash
   npm run start
   ```

3. **Access the application:**
   - Frontend: [http://localhost:3000](http://localhost:3000)
   - Backend API: [http://localhost:5000](http://localhost:5000)

The startup script will automatically:
- ✅ Check all prerequisites
- ✅ Install dependencies for both backend and frontend
- ✅ Start both servers in the same terminal
- ✅ Press Ctrl+C to stop both servers

### **Option 2: Manual Setup**

If you prefer to set up each component manually:

#### **Backend Setup**

1. **Navigate to backend directory:**
   ```bash
   cd note-app-be
   ```

2. **Create virtual environment:**
   ```bash
   python -m venv venv

   # Windows
   venv\Scripts\activate

   # Linux/macOS
   source venv/bin/activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Start Flask server:**
   ```bash
   python app.py
   ```

#### **Frontend Setup**

1. **Navigate to frontend directory:**
   ```bash
   cd note-app-frontend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Start development server:**
   ```bash
   npm run dev
   ```

## 📁 Project Structure

```
note-app/
├── 📄 README.md                    # This file
├── 🚀 start-app.bat               # Windows startup script
├── 🚀 start-app.sh                # Unix/Linux startup script
├── 📁 logs/                       # Application logs (created automatically)
├── 📁 note-app-be/                # Flask Backend
│   ├── 📄 app.py                  # Main Flask application
│   ├── 📄 models.py               # Database models
│   ├── 📄 config.py               # Configuration settings
│   ├── 📄 requirements.txt        # Python dependencies
│   ├── 📁 venv/                   # Python virtual environment
│   └── 📄 .env                    # Environment variables (create this)
└── 📁 note-app-frontend/          # Next.js Frontend
    ├── 📁 src/
    │   ├── 📁 app/                # Next.js App Router pages
    │   │   ├── 📄 layout.tsx      # Root layout
    │   │   ├── 📄 page.tsx        # Home page
    │   │   └── 📁 notes/          # Note-related pages
    │   ├── 📁 components/         # Reusable React components
    │   │   ├── 📁 Layout/         # Layout components
    │   │   ├── 📄 NoteCard.tsx    # Individual note display
    │   │   ├── 📄 NoteForm.tsx    # Create/edit forms
    │   │   └── 📄 ...             # Other components
    │   ├── 📁 lib/                # Utilities and services
    │   │   ├── 📄 api.ts          # API client
    │   │   ├── 📄 store.ts        # State management
    │   │   └── 📄 types.ts        # TypeScript definitions
    │   └── 📁 styles/             # CSS and styling
    ├── 📄 package.json            # Node.js dependencies
    ├── 📄 tailwind.config.js      # Tailwind CSS configuration
    ├── 📄 next.config.js          # Next.js configuration
    └── 📄 .env.local              # Frontend environment variables
```

## 🔌 API Documentation

The Flask backend provides a RESTful API for note management:

### **Base URL**
```
http://localhost:5000
```

### **Endpoints**

#### **Get All Notes**
```http
GET /notes
```
**Response:**
```json
[
  {
    "id": 1,
    "title": "My First Note",
    "content": "This is the content of my note.",
    "created_at": "2025-01-15T10:30:00.000Z",
    "updated_at": "2025-01-15T10:30:00.000Z"
  }
]
```

#### **Get Single Note**
```http
GET /notes/{id}
```
**Response:**
```json
{
  "id": 1,
  "title": "My First Note",
  "content": "This is the content of my note.",
  "created_at": "2025-01-15T10:30:00.000Z",
  "updated_at": "2025-01-15T10:30:00.000Z"
}
```

#### **Create Note**
```http
POST /notes
Content-Type: application/json

{
  "title": "New Note Title",
  "content": "Note content here"
}
```
**Response:** `201 Created` with note object

#### **Update Note**
```http
PUT /notes/{id}
Content-Type: application/json

{
  "title": "Updated Title",
  "content": "Updated content"
}
```
**Response:** `200 OK` with updated note object

#### **Delete Note**
```http
DELETE /notes/{id}
```
**Response:**
```json
{
  "message": "Note deleted successfully"
}
```

### **Error Responses**
All endpoints return appropriate HTTP status codes and error messages:
```json
{
  "error": "Error description here"
}
```

## 🛠️ Development Workflow

### **Making Changes**

1. **Backend Changes:**
   - Edit files in `note-app-be/`
   - Flask auto-reloads in development mode
   - Test API endpoints using browser or Postman

2. **Frontend Changes:**
   - Edit files in `note-app-frontend/src/`
   - Next.js hot-reloads automatically
   - Changes appear instantly in browser

### **Adding New Features**

1. **Backend (API Endpoints):**
   ```python
   # In note-app-be/app.py
   @app.route('/notes/search', methods=['GET'])
   def search_notes():
       # Implementation here
       pass
   ```

2. **Frontend (Components):**
   ```typescript
   // In note-app-frontend/src/components/
   export function NewComponent() {
     // Implementation here
   }
   ```

### **Database Changes**

The application uses SQLite by default. To modify the database:

1. **Update models in `note-app-be/models.py`**
2. **Delete existing database file (if any)**
3. **Restart the Flask server** (tables are created automatically)

### **Styling Changes**

The retro theme is defined in:
- `note-app-frontend/tailwind.config.js` - Color palette and theme
- `note-app-frontend/src/app/globals.css` - Custom CSS classes

## 🔧 Configuration

### **Backend Configuration**

Create `note-app-be/.env` file:
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=retro_notes
DB_USER=root
DB_PASSWORD=your_password

# Flask Configuration
SECRET_KEY=your-secret-key-here
FLASK_ENV=development
DEBUG=True
```

### **Frontend Configuration**

Edit `note-app-frontend/.env.local`:
```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:5000

# Optional: Analytics, etc.
NEXT_PUBLIC_GA_ID=your-google-analytics-id
```

## 🐛 Troubleshooting

### **Common Issues**

#### **Port Already in Use**
```bash
# Find and kill process using port 5000 (backend)
lsof -ti:5000 | xargs kill -9

# Find and kill process using port 3000 (frontend)
lsof -ti:3000 | xargs kill -9
```

#### **Python Virtual Environment Issues**
```bash
# Delete and recreate virtual environment
rm -rf note-app-be/venv
cd note-app-be
python -m venv venv
source venv/bin/activate  # Linux/macOS
# or
venv\Scripts\activate     # Windows
pip install -r requirements.txt
```

#### **Node.js Dependency Issues**
```bash
# Clear npm cache and reinstall
cd note-app-frontend
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

#### **Database Connection Issues**
1. Check database configuration in `.env` file
2. Ensure database server is running
3. Verify connection credentials
4. Check firewall settings

#### **CORS Issues**
If you encounter CORS errors:
1. Ensure Flask backend includes CORS headers
2. Check that frontend is making requests to correct URL
3. Verify API base URL in frontend configuration

### **Log Files**

When using startup scripts, logs are saved to:
- `logs/backend.log` - Flask server logs
- `logs/frontend.log` - Next.js development server logs

### **Debug Mode**

#### **Backend Debug:**
```bash
cd note-app-be
source venv/bin/activate
export FLASK_ENV=development
export DEBUG=True
python app.py
```

#### **Frontend Debug:**
```bash
cd note-app-frontend
npm run dev
# Open browser developer tools (F12)
```

## 🧪 Testing

### **Manual Testing Checklist**

- [ ] **Create Note**: Can create new notes with title and content
- [ ] **Read Notes**: Can view list of all notes and individual note details
- [ ] **Update Note**: Can edit existing notes and save changes
- [ ] **Delete Note**: Can delete notes with confirmation dialog
- [ ] **Search**: Can search notes by title and content
- [ ] **Sort**: Can sort notes by different criteria
- [ ] **Responsive**: Works on mobile, tablet, and desktop
- [ ] **Theme Toggle**: Can switch between light and dark themes
- [ ] **Error Handling**: Proper error messages for failed operations

### **API Testing**

Use curl or Postman to test API endpoints:

```bash
# Test get all notes
curl http://localhost:5000/notes

# Test create note
curl -X POST http://localhost:5000/notes \
  -H "Content-Type: application/json" \
  -d '{"title":"Test Note","content":"Test content"}'

# Test get single note
curl http://localhost:5000/notes/1

# Test update note
curl -X PUT http://localhost:5000/notes/1 \
  -H "Content-Type: application/json" \
  -d '{"title":"Updated Title","content":"Updated content"}'

# Test delete note
curl -X DELETE http://localhost:5000/notes/1
```

## 🚀 Deployment

### **Production Build**

#### **Frontend:**
```bash
cd note-app-frontend
npm run build
npm run start
```

#### **Backend:**
```bash
cd note-app-be
source venv/bin/activate
export FLASK_ENV=production
export DEBUG=False
python app.py
```

### **Environment Variables for Production**

Update configuration files with production values:
- Database connection strings
- Secret keys
- API URLs
- Debug flags (set to False)

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch:** `git checkout -b feature/amazing-feature`
3. **Make your changes** and test thoroughly
4. **Commit your changes:** `git commit -m 'Add amazing feature'`
5. **Push to the branch:** `git push origin feature/amazing-feature`
6. **Open a Pull Request**

### **Development Guidelines**

- Follow existing code style and conventions
- Add comments for complex logic
- Test all changes thoroughly
- Update documentation as needed
- Ensure responsive design for frontend changes

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Design Inspiration**: Vintage computing aesthetics and retro design principles
- **Typography**: Google Fonts (Courier Prime, Crimson Text)
- **Icons**: Lucide React icon library
- **Color Palette**: Inspired by 1980s-90s computing and vintage notebooks

## 📞 Support

If you encounter any issues or have questions:

1. **Check this README** for common solutions
2. **Review the troubleshooting section** above
3. **Check log files** in the `logs/` directory
4. **Open an issue** on the project repository
5. **Contact the development team**

---

**Happy note-taking with retro vibes! 📝✨**

*Built with ❤️ using modern technologies and vintage aesthetics*
