/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // Retro color palette
        retro: {
          cream: '#F5F5DC',
          beige: '#F5E6D3',
          orange: '#FF8C42',
          'orange-dark': '#E07A00',
          brown: '#8B4513',
          'brown-dark': '#654321',
          'brown-light': '#D2B48C',
          green: '#9CAF88',
          'green-dark': '#7A8471',
          yellow: '#FFD700',
          red: '#CD5C5C',
          'paper': '#FDF6E3',
          'paper-dark': '#F4E4BC',
        },
        // Dark theme variants
        'retro-dark': {
          bg: '#2C1810',
          'bg-light': '#3D2317',
          text: '#F5E6D3',
          'text-muted': '#D2B48C',
          accent: '#FF8C42',
          'accent-dark': '#E07A00',
        }
      },
      fontFamily: {
        'mono': ['Courier New', 'monospace'],
        'typewriter': ['American Typewriter', 'Courier New', 'monospace'],
        'retro': ['Georgia', 'Times New Roman', 'serif'],
      },
      boxShadow: {
        'retro': '4px 4px 8px rgba(139, 69, 19, 0.3)',
        'retro-inset': 'inset 2px 2px 4px rgba(139, 69, 19, 0.2)',
        'paper': '0 4px 8px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06)',
      },
      animation: {
        'typewriter': 'typewriter 2s steps(40) 1s 1 normal both',
        'blink': 'blink 1s infinite',
      },
      keyframes: {
        typewriter: {
          'from': { width: '0' },
          'to': { width: '100%' }
        },
        blink: {
          '0%, 50%': { opacity: '1' },
          '51%, 100%': { opacity: '0' }
        }
      }
    },
  },
  plugins: [],
  darkMode: 'class',
}
