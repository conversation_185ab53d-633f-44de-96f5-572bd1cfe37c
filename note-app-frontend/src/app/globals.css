@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import retro fonts */
@import url('https://fonts.googleapis.com/css2?family=Courier+Prime:ital,wght@0,400;0,700;1,400;1,700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;0,600;1,400;1,600&display=swap');

:root {
  /* Light theme colors */
  --retro-cream: #F5F5DC;
  --retro-beige: #F5E6D3;
  --retro-orange: #FF8C42;
  --retro-orange-dark: #E07A00;
  --retro-brown: #8B4513;
  --retro-brown-dark: #654321;
  --retro-brown-light: #D2B48C;
  --retro-green: #9CAF88;
  --retro-green-dark: #7A8471;
  --retro-yellow: #FFD700;
  --retro-red: #CD5C5C;
  --retro-paper: #FDF6E3;
  --retro-paper-dark: #F4E4BC;
  
  /* Dark theme colors */
  --retro-dark-bg: #2C1810;
  --retro-dark-bg-light: #3D2317;
  --retro-dark-text: #F5E6D3;
  --retro-dark-text-muted: #D2B48C;
  --retro-dark-accent: #FF8C42;
  --retro-dark-accent-dark: #E07A00;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Crimson Text', Georgia, serif;
}

body {
  background: linear-gradient(135deg, var(--retro-paper) 0%, var(--retro-beige) 100%);
  color: var(--retro-brown-dark);
  min-height: 100vh;
  transition: all 0.3s ease;
}

.dark body {
  background: linear-gradient(135deg, var(--retro-dark-bg) 0%, var(--retro-dark-bg-light) 100%);
  color: var(--retro-dark-text);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: var(--retro-paper-dark);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: var(--retro-brown-light);
  border-radius: 6px;
  border: 2px solid var(--retro-paper-dark);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--retro-brown);
}

.dark ::-webkit-scrollbar-track {
  background: var(--retro-dark-bg-light);
}

.dark ::-webkit-scrollbar-thumb {
  background: var(--retro-dark-text-muted);
  border-color: var(--retro-dark-bg-light);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: var(--retro-dark-text);
}

/* Retro paper texture */
.paper-texture {
  background-image: 
    radial-gradient(circle at 1px 1px, rgba(139, 69, 19, 0.1) 1px, transparent 0);
  background-size: 20px 20px;
}

.dark .paper-texture {
  background-image: 
    radial-gradient(circle at 1px 1px, rgba(245, 230, 211, 0.05) 1px, transparent 0);
}

/* Retro button styles */
.retro-button {
  @apply px-6 py-3 rounded-lg font-semibold transition-all duration-200;
  @apply bg-retro-orange text-white shadow-retro;
  @apply hover:bg-retro-orange-dark hover:shadow-lg hover:-translate-y-0.5;
  @apply active:translate-y-0 active:shadow-retro-inset;
  font-family: 'Courier Prime', monospace;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.retro-button:disabled {
  @apply opacity-50 cursor-not-allowed;
  @apply hover:translate-y-0 hover:shadow-retro;
}

.retro-button-secondary {
  @apply bg-retro-brown text-retro-cream;
  @apply hover:bg-retro-brown-dark;
}

.retro-button-danger {
  @apply bg-retro-red text-white;
  @apply hover:bg-red-600;
}

.dark .retro-button {
  @apply bg-retro-dark-accent text-retro-dark-bg;
  @apply hover:bg-retro-dark-accent-dark;
}

/* Retro input styles */
.retro-input {
  @apply w-full px-4 py-3 rounded-lg border-2 border-retro-brown-light;
  @apply bg-retro-paper text-retro-brown-dark;
  @apply focus:border-retro-orange focus:outline-none focus:ring-2 focus:ring-retro-orange/20;
  @apply transition-all duration-200;
  font-family: 'Courier Prime', monospace;
}

.retro-input::placeholder {
  @apply text-retro-brown opacity-60;
  font-style: italic;
}

.dark .retro-input {
  @apply bg-retro-dark-bg-light border-retro-dark-text-muted text-retro-dark-text;
  @apply focus:border-retro-dark-accent focus:ring-retro-dark-accent/20;
}

.dark .retro-input::placeholder {
  @apply text-retro-dark-text-muted opacity-60;
}

/* Retro textarea */
.retro-textarea {
  @apply retro-input resize-none min-h-[120px];
  font-family: 'Courier Prime', monospace;
  line-height: 1.6;
}

/* Retro card styles */
.retro-card {
  @apply bg-retro-paper border-2 border-retro-brown-light rounded-lg p-6;
  @apply shadow-paper transition-all duration-200;
  @apply hover:shadow-retro hover:-translate-y-1;
}

.dark .retro-card {
  @apply bg-retro-dark-bg-light border-retro-dark-text-muted;
}

/* Retro typography */
.retro-heading {
  font-family: 'Crimson Text', Georgia, serif;
  @apply font-semibold text-retro-brown-dark;
}

.dark .retro-heading {
  @apply text-retro-dark-text;
}

.retro-text {
  font-family: 'Courier Prime', monospace;
  @apply text-retro-brown;
  line-height: 1.6;
}

.dark .retro-text {
  @apply text-retro-dark-text-muted;
}

/* Typewriter animation */
.typewriter {
  overflow: hidden;
  border-right: 3px solid var(--retro-orange);
  white-space: nowrap;
  animation: typewriter 2s steps(40) 1s 1 normal both, blink 1s infinite;
}

@keyframes typewriter {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink {
  0%, 50% { border-color: var(--retro-orange); }
  51%, 100% { border-color: transparent; }
}

/* Loading spinner */
.retro-spinner {
  @apply w-8 h-8 border-4 border-retro-brown-light border-t-retro-orange rounded-full;
  animation: spin 1s linear infinite;
}

.dark .retro-spinner {
  @apply border-retro-dark-text-muted border-t-retro-dark-accent;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error and success states */
.retro-error {
  @apply text-retro-red bg-red-50 border border-red-200 rounded-lg p-4;
  font-family: 'Courier Prime', monospace;
}

.dark .retro-error {
  @apply bg-red-900/20 border-red-800 text-red-300;
}

.retro-success {
  @apply text-green-700 bg-green-50 border border-green-200 rounded-lg p-4;
  font-family: 'Courier Prime', monospace;
}

.dark .retro-success {
  @apply bg-green-900/20 border-green-800 text-green-300;
}

/* Retro modal/dialog */
.retro-modal-overlay {
  @apply fixed inset-0 bg-black/50 backdrop-blur-sm z-50;
  @apply flex items-center justify-center p-4;
}

.retro-modal {
  @apply bg-retro-paper border-4 border-retro-brown rounded-lg p-8 max-w-md w-full;
  @apply shadow-2xl transform transition-all duration-200;
}

.dark .retro-modal {
  @apply bg-retro-dark-bg-light border-retro-dark-text-muted;
}

/* Retro navigation */
.retro-nav {
  @apply bg-retro-brown text-retro-cream shadow-retro;
  background-image: linear-gradient(45deg, transparent 25%, rgba(255,255,255,0.1) 25%, rgba(255,255,255,0.1) 50%, transparent 50%, transparent 75%, rgba(255,255,255,0.1) 75%);
  background-size: 20px 20px;
}

.dark .retro-nav {
  @apply bg-retro-dark-bg text-retro-dark-text;
}

/* Utility classes */
.retro-shadow {
  box-shadow: 4px 4px 8px rgba(139, 69, 19, 0.3);
}

.retro-shadow-inset {
  box-shadow: inset 2px 2px 4px rgba(139, 69, 19, 0.2);
}

.retro-border {
  @apply border-2 border-retro-brown-light;
}

.dark .retro-border {
  @apply border-retro-dark-text-muted;
}

/* Focus styles */
.retro-focus {
  @apply focus:outline-none focus:ring-2 focus:ring-retro-orange focus:ring-offset-2;
}

.dark .retro-focus {
  @apply focus:ring-retro-dark-accent focus:ring-offset-retro-dark-bg;
}
