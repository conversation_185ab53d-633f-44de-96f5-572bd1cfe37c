import type { Metadata } from 'next';
import './globals.css';
import { Header } from '@/components/Layout/Header';
import { ThemeProvider } from '@/components/ThemeProvider';
import { ErrorBoundary } from '@/components/ErrorBoundary';

export const metadata: Metadata = {
  title: 'Retro Notes - Your Vintage Note-Taking App',
  description: 'A retro-styled note-taking application with vintage aesthetics',
  keywords: ['notes', 'retro', 'vintage', 'note-taking', 'productivity'],
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="min-h-screen paper-texture">
        <ErrorBoundary>
          <ThemeProvider>
            <div className="min-h-screen flex flex-col">
              <Header />
              <main className="flex-1 container mx-auto px-4 py-8 max-w-6xl">
                {children}
              </main>
              <footer className="retro-nav py-4 text-center">
                <p className="retro-text text-sm opacity-80">
                  © 2025 Retro Notes - Crafted with vintage vibes
                </p>
              </footer>
            </div>
          </ThemeProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
