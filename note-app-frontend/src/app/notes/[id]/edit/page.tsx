'use client';

import { useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useNotesStore } from '@/lib/store';
import { NoteForm } from '@/components/NoteForm';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { UpdateNoteRequest } from '@/lib/types';

export default function EditNotePage() {
  const params = useParams();
  const router = useRouter();
  const noteId = parseInt(params.id as string);
  
  const { 
    currentNote, 
    isLoading, 
    error, 
    fetchNote, 
    updateNote, 
    clearCurrentNote 
  } = useNotesStore();

  // Fetch note on mount
  useEffect(() => {
    if (noteId) {
      fetchNote(noteId);
    }

    // Cleanup on unmount
    return () => {
      clearCurrentNote();
    };
  }, [noteId, fetchNote, clearCurrentNote]);

  const handleSubmit = async (data: UpdateNoteRequest) => {
    try {
      await updateNote(noteId, data);
      router.push(`/notes/${noteId}`);
    } catch (error) {
      // Error is handled by the store
      console.error('Failed to update note:', error);
    }
  };

  const handleCancel = () => {
    router.push(`/notes/${noteId}`);
  };

  // Loading state
  if (isLoading && !currentNote) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="retro-card text-center">
          <LoadingSpinner size="lg" />
          <p className="retro-text mt-4">Loading note for editing...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="retro-error">
          <h2 className="font-semibold text-lg mb-2">Failed to Load Note</h2>
          <p className="mb-4">{error}</p>
          <div className="flex gap-4">
            <button
              onClick={() => fetchNote(noteId)}
              className="retro-button"
            >
              Try Again
            </button>
            <button
              onClick={() => router.push('/')}
              className="retro-button retro-button-secondary"
            >
              Back to Notes
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Note not found
  if (!currentNote) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="retro-card text-center">
          <h2 className="retro-heading text-2xl mb-4">Note Not Found</h2>
          <p className="retro-text mb-6">
            The note you're trying to edit doesn't exist or may have been deleted.
          </p>
          <button
            onClick={() => router.push('/')}
            className="retro-button"
          >
            Back to Notes
          </button>
        </div>
      </div>
    );
  }

  return (
    <NoteForm
      mode="edit"
      note={currentNote}
      onSubmit={handleSubmit}
      onCancel={handleCancel}
      isLoading={isLoading}
    />
  );
}
