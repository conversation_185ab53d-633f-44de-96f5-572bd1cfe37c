'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Edit, Trash2, Calendar, Clock, FileText } from 'lucide-react';
import { useNotesStore } from '@/lib/store';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { DeleteConfirmDialog } from '@/components/DeleteConfirmDialog';
import { formatDate, formatRelativeTime, cn } from '@/lib/utils';

export default function NoteDetailPage() {
  const params = useParams();
  const router = useRouter();
  const noteId = parseInt(params.id as string);
  
  const { 
    currentNote, 
    isLoading, 
    error, 
    fetchNote, 
    deleteNote, 
    clearCurrentNote 
  } = useNotesStore();

  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Fetch note on mount
  useEffect(() => {
    if (noteId) {
      fetchNote(noteId);
    }

    // Cleanup on unmount
    return () => {
      clearCurrentNote();
    };
  }, [noteId, fetchNote, clearCurrentNote]);

  const handleDelete = async () => {
    try {
      await deleteNote(noteId);
      router.push('/');
    } catch (error) {
      console.error('Failed to delete note:', error);
    }
    setShowDeleteDialog(false);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="retro-card text-center">
          <LoadingSpinner size="lg" />
          <p className="retro-text mt-4">Loading your vintage note...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="retro-error">
          <h2 className="font-semibold text-lg mb-2">Failed to Load Note</h2>
          <p className="mb-4">{error}</p>
          <div className="flex gap-4">
            <button
              onClick={() => fetchNote(noteId)}
              className="retro-button"
            >
              Try Again
            </button>
            <Link
              href="/"
              className="retro-button retro-button-secondary"
            >
              Back to Notes
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Note not found
  if (!currentNote) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="retro-card text-center">
          <FileText className="w-16 h-16 text-retro-brown-light mx-auto mb-4" />
          <h2 className="retro-heading text-2xl mb-4">Note Not Found</h2>
          <p className="retro-text mb-6">
            The note you're looking for doesn't exist or may have been deleted.
          </p>
          <Link
            href="/"
            className="retro-button"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Notes
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Navigation */}
        <div className="flex items-center justify-between">
          <Link
            href="/"
            className={cn(
              'flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200',
              'bg-retro-brown-light text-retro-brown hover:bg-retro-beige',
              'hover:shadow-retro hover:-translate-y-0.5'
            )}
          >
            <ArrowLeft className="w-4 h-4" />
            <span className="font-mono font-semibold">Back to Notes</span>
          </Link>

          <div className="flex items-center space-x-3">
            <Link
              href={`/notes/${currentNote.id}/edit`}
              className={cn(
                'flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200',
                'bg-retro-orange text-white hover:bg-retro-orange-dark',
                'hover:shadow-retro hover:-translate-y-0.5'
              )}
            >
              <Edit className="w-4 h-4" />
              <span className="font-mono font-semibold">Edit</span>
            </Link>

            <button
              onClick={() => setShowDeleteDialog(true)}
              className={cn(
                'flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200',
                'bg-retro-red text-white hover:bg-red-600',
                'hover:shadow-retro hover:-translate-y-0.5'
              )}
            >
              <Trash2 className="w-4 h-4" />
              <span className="font-mono font-semibold">Delete</span>
            </button>
          </div>
        </div>

        {/* Note Header */}
        <div className="retro-card">
          <div className="flex items-start justify-between mb-6">
            <div className="flex-1 min-w-0">
              <h1 className="retro-heading text-3xl md:text-4xl mb-4 break-words">
                {currentNote.title}
              </h1>
              
              <div className="flex flex-wrap items-center gap-4 text-sm opacity-75">
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4" />
                  <span className="retro-text">
                    Created {formatDate(currentNote.created_at)}
                  </span>
                </div>
                
                {currentNote.updated_at !== currentNote.created_at && (
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4" />
                    <span className="retro-text">
                      Updated {formatRelativeTime(currentNote.updated_at)}
                    </span>
                  </div>
                )}
                
                <div className="flex items-center space-x-2">
                  <span className="px-3 py-1 bg-retro-orange/20 text-retro-orange-dark rounded-full text-xs font-mono font-semibold">
                    #{currentNote.id}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Note Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-retro-beige/50 rounded-lg border-2 border-retro-brown-light">
            <div className="text-center">
              <div className="retro-heading text-lg font-bold text-retro-orange">
                {currentNote.content.length}
              </div>
              <div className="retro-text text-xs">Characters</div>
            </div>
            
            <div className="text-center">
              <div className="retro-heading text-lg font-bold text-retro-green">
                {currentNote.content.split(/\s+/).filter(word => word.length > 0).length}
              </div>
              <div className="retro-text text-xs">Words</div>
            </div>
            
            <div className="text-center">
              <div className="retro-heading text-lg font-bold text-retro-brown">
                {currentNote.content.split('\n').length}
              </div>
              <div className="retro-text text-xs">Lines</div>
            </div>
            
            <div className="text-center">
              <div className="retro-heading text-lg font-bold text-retro-red">
                {Math.ceil(currentNote.content.length / 250) || 1}
              </div>
              <div className="retro-text text-xs">Est. Read Min</div>
            </div>
          </div>
        </div>

        {/* Note Content */}
        <div className="retro-card">
          <h2 className="retro-heading text-xl mb-6 flex items-center">
            <FileText className="w-5 h-5 mr-2" />
            Note Content
          </h2>
          
          {currentNote.content ? (
            <div className="prose prose-retro max-w-none">
              <div className="retro-text leading-relaxed whitespace-pre-wrap break-words">
                {currentNote.content}
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <FileText className="w-16 h-16 text-retro-brown-light mx-auto mb-4" />
              <p className="retro-text italic opacity-60">
                This note doesn't have any content yet.
              </p>
              <Link
                href={`/notes/${currentNote.id}/edit`}
                className="retro-button mt-4 inline-flex items-center"
              >
                <Edit className="w-4 h-4 mr-2" />
                Add Content
              </Link>
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="retro-card">
          <h3 className="retro-heading text-lg mb-4">Quick Actions</h3>
          <div className="flex flex-wrap gap-4">
            <Link
              href={`/notes/${currentNote.id}/edit`}
              className="retro-button"
            >
              <Edit className="w-4 h-4 mr-2" />
              Edit Note
            </Link>
            
            <button
              onClick={() => {
                navigator.clipboard.writeText(currentNote.content);
                // You could add a toast notification here
              }}
              className="retro-button retro-button-secondary"
            >
              Copy Content
            </button>
            
            <Link
              href="/"
              className="retro-button retro-button-secondary"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              All Notes
            </Link>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmDialog
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        onConfirm={handleDelete}
        noteTitle={currentNote.title}
      />
    </>
  );
}
