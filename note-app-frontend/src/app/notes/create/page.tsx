'use client';

import { useRouter } from 'next/navigation';
import { useNotesStore } from '@/lib/store';
import { NoteForm } from '@/components/NoteForm';
import { CreateNoteRequest } from '@/lib/types';

export default function CreateNotePage() {
  const router = useRouter();
  const { createNote, isLoading } = useNotesStore();

  const handleSubmit = async (data: CreateNoteRequest) => {
    try {
      const newNote = await createNote(data);
      router.push(`/notes/${newNote.id}`);
    } catch (error) {
      // Error is handled by the store
      console.error('Failed to create note:', error);
    }
  };

  const handleCancel = () => {
    router.push('/');
  };

  return (
    <NoteForm
      mode="create"
      onSubmit={handleSubmit}
      onCancel={handleCancel}
      isLoading={isLoading}
    />
  );
}
