'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { PlusCircle, Home, Moon, Sun, BookOpen } from 'lucide-react';
import { useNotesStore } from '@/lib/store';
import { cn } from '@/lib/utils';

export function Header() {
  const pathname = usePathname();
  const { theme, toggleTheme } = useNotesStore();

  const navItems = [
    { href: '/', label: 'Home', icon: Home },
    { href: '/notes/create', label: 'New Note', icon: PlusCircle },
  ];

  return (
    <header className="retro-nav sticky top-0 z-40 border-b-4 border-retro-brown-dark">
      <div className="container mx-auto px-4 py-4 max-w-6xl">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link 
            href="/" 
            className="flex items-center space-x-3 hover:opacity-80 transition-opacity"
          >
            <BookOpen className="w-8 h-8" />
            <div>
              <h1 className="text-2xl font-bold font-retro">
                Retro Notes
              </h1>
              <p className="text-xs opacity-75 font-mono">
                Vintage vibes, modern notes
              </p>
            </div>
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            {navItems.map(({ href, label, icon: Icon }) => (
              <Link
                key={href}
                href={href}
                className={cn(
                  'flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200',
                  'hover:bg-white/10 hover:shadow-retro-inset',
                  'font-mono text-sm font-semibold',
                  pathname === href && 'bg-white/20 shadow-retro-inset'
                )}
              >
                <Icon className="w-4 h-4" />
                <span>{label}</span>
              </Link>
            ))}
          </nav>

          {/* Theme Toggle */}
          <button
            onClick={toggleTheme}
            className={cn(
              'p-3 rounded-lg transition-all duration-200',
              'hover:bg-white/10 hover:shadow-retro-inset',
              'focus:outline-none focus:ring-2 focus:ring-white/20'
            )}
            aria-label={theme.isDark ? 'Switch to light mode' : 'Switch to dark mode'}
          >
            {theme.isDark ? (
              <Sun className="w-5 h-5" />
            ) : (
              <Moon className="w-5 h-5" />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        <nav className="md:hidden mt-4 flex items-center justify-center space-x-4">
          {navItems.map(({ href, label, icon: Icon }) => (
            <Link
              key={href}
              href={href}
              className={cn(
                'flex flex-col items-center space-y-1 px-3 py-2 rounded-lg transition-all duration-200',
                'hover:bg-white/10 hover:shadow-retro-inset',
                'font-mono text-xs font-semibold',
                pathname === href && 'bg-white/20 shadow-retro-inset'
              )}
            >
              <Icon className="w-4 h-4" />
              <span>{label}</span>
            </Link>
          ))}
        </nav>
      </div>
    </header>
  );
}
