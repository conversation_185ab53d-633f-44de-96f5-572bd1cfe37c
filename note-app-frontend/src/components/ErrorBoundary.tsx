'use client';

import React from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error!} resetError={this.resetError} />;
      }

      return <DefaultErrorFallback error={this.state.error!} resetError={this.resetError} />;
    }

    return this.props.children;
  }
}

function DefaultErrorFallback({ error, resetError }: { error: Error; resetError: () => void }) {
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="retro-card max-w-md w-full text-center">
        <div className="p-4 bg-retro-red/20 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center">
          <AlertTriangle className="w-10 h-10 text-retro-red" />
        </div>
        
        <h1 className="retro-heading text-2xl mb-4">
          Oops! Something went wrong
        </h1>
        
        <p className="retro-text mb-6">
          We encountered an unexpected error. Don't worry, your notes are safe!
        </p>
        
        <details className="mb-6 text-left">
          <summary className="retro-text text-sm cursor-pointer hover:text-retro-orange">
            Technical details
          </summary>
          <pre className="mt-2 p-3 bg-retro-beige rounded text-xs font-mono overflow-auto">
            {error.message}
          </pre>
        </details>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={resetError}
            className="retro-button inline-flex items-center justify-center"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </button>
          
          <button
            onClick={() => window.location.reload()}
            className="retro-button retro-button-secondary inline-flex items-center justify-center"
          >
            Reload Page
          </button>
        </div>
      </div>
    </div>
  );
}
