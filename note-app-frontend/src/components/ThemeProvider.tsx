'use client';

import { useEffect } from 'react';
import { useNotesStore } from '@/lib/store';

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const { theme } = useNotesStore();

  useEffect(() => {
    // Apply theme class to document
    if (theme.isDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [theme.isDark]);

  return <>{children}</>;
}
