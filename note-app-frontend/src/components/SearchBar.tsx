'use client';

import { useState, useEffect } from 'react';
import { Search, SortAsc, SortDesc } from 'lucide-react';
import { useNotesStore } from '@/lib/store';
import { debounce } from '@/lib/utils';
import { cn } from '@/lib/utils';

export function SearchBar() {
  const { searchFilters, setSearchFilters } = useNotesStore();
  const [localQuery, setLocalQuery] = useState(searchFilters.query);

  // Debounced search
  const debouncedSearch = debounce((query: string) => {
    setSearchFilters({ query });
  }, 300);

  useEffect(() => {
    debouncedSearch(localQuery);
  }, [localQuery, debouncedSearch]);

  const handleSortChange = (sortBy: typeof searchFilters.sortBy) => {
    const newOrder = 
      searchFilters.sortBy === sortBy && searchFilters.sortOrder === 'desc' 
        ? 'asc' 
        : 'desc';
    
    setSearchFilters({ sortBy, sortOrder: newOrder });
  };

  return (
    <div className="retro-card mb-8">
      <div className="flex flex-col md:flex-row gap-4">
        {/* Search Input */}
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-retro-brown opacity-60" />
          <input
            type="text"
            placeholder="Search your notes..."
            value={localQuery}
            onChange={(e) => setLocalQuery(e.target.value)}
            className="retro-input pl-12 w-full"
          />
        </div>

        {/* Sort Controls */}
        <div className="flex gap-2">
          {(['title', 'created_at', 'updated_at'] as const).map((sortBy) => {
            const isActive = searchFilters.sortBy === sortBy;
            const isDesc = searchFilters.sortOrder === 'desc';
            
            return (
              <button
                key={sortBy}
                onClick={() => handleSortChange(sortBy)}
                className={cn(
                  'flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200',
                  'border-2 font-mono text-sm font-semibold',
                  isActive
                    ? 'bg-retro-orange text-white border-retro-orange-dark shadow-retro-inset'
                    : 'bg-retro-paper border-retro-brown-light text-retro-brown hover:bg-retro-beige'
                )}
              >
                <span className="capitalize">
                  {sortBy === 'created_at' ? 'Created' : 
                   sortBy === 'updated_at' ? 'Updated' : 
                   'Title'}
                </span>
                {isActive && (
                  isDesc ? <SortDesc className="w-4 h-4" /> : <SortAsc className="w-4 h-4" />
                )}
              </button>
            );
          })}
        </div>
      </div>

      {/* Active Filters Display */}
      {(searchFilters.query || searchFilters.sortBy !== 'updated_at') && (
        <div className="mt-4 pt-4 border-t border-retro-brown-light">
          <div className="flex flex-wrap gap-2 items-center">
            <span className="retro-text text-sm font-semibold">Active filters:</span>
            
            {searchFilters.query && (
              <span className="px-3 py-1 bg-retro-orange/20 text-retro-orange-dark rounded-full text-sm font-mono">
                Search: "{searchFilters.query}"
              </span>
            )}
            
            {searchFilters.sortBy !== 'updated_at' && (
              <span className="px-3 py-1 bg-retro-green/20 text-retro-green-dark rounded-full text-sm font-mono">
                Sort: {searchFilters.sortBy === 'created_at' ? 'Created' : 'Title'} 
                ({searchFilters.sortOrder})
              </span>
            )}
            
            <button
              onClick={() => {
                setLocalQuery('');
                setSearchFilters({ query: '', sortBy: 'updated_at', sortOrder: 'desc' });
              }}
              className="px-3 py-1 bg-retro-red/20 text-retro-red rounded-full text-sm font-mono hover:bg-retro-red/30 transition-colors"
            >
              Clear all
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
