'use client';

import { useEffect } from 'react';
import { AlertTriangle, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface DeleteConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  noteTitle: string;
}

export function DeleteConfirmDialog({
  isOpen,
  onClose,
  onConfirm,
  noteTitle,
}: DeleteConfirmDialogProps) {
  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="retro-modal-overlay" onClick={onClose}>
      <div 
        className="retro-modal animate-in zoom-in-95 duration-200"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-retro-red/20 rounded-lg">
              <AlertTriangle className="w-6 h-6 text-retro-red" />
            </div>
            <h2 className="retro-heading text-xl">
              Delete Note
            </h2>
          </div>
          
          <button
            onClick={onClose}
            className={cn(
              'p-2 rounded-lg transition-all duration-200',
              'hover:bg-retro-brown-light/20 hover:shadow-retro-inset',
              'focus:outline-none focus:ring-2 focus:ring-retro-orange/20'
            )}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="mb-8">
          <p className="retro-text mb-4">
            Are you sure you want to delete this note? This action cannot be undone.
          </p>
          
          <div className="p-4 bg-retro-red/10 border-2 border-retro-red/20 rounded-lg">
            <p className="font-semibold text-retro-brown-dark mb-1">
              Note to be deleted:
            </p>
            <p className="retro-text font-mono text-sm break-words">
              "{noteTitle}"
            </p>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-4">
          <button
            onClick={onClose}
            className={cn(
              'px-6 py-3 rounded-lg font-semibold transition-all duration-200',
              'bg-retro-brown-light text-retro-brown border-2 border-retro-brown-light',
              'hover:bg-retro-beige hover:shadow-retro',
              'focus:outline-none focus:ring-2 focus:ring-retro-brown/20'
            )}
          >
            Cancel
          </button>
          
          <button
            onClick={onConfirm}
            className={cn(
              'retro-button retro-button-danger',
              'focus:outline-none focus:ring-2 focus:ring-retro-red/20'
            )}
          >
            Delete Note
          </button>
        </div>
      </div>
    </div>
  );
}
