'use client';

import Link from 'next/link';
import { useState } from 'react';
import { Edit, Trash2, Eye, Calendar, Clock } from 'lucide-react';
import { Note } from '@/lib/types';
import { formatRelativeTime, getPreviewText, cn } from '@/lib/utils';
import { DeleteConfirmDialog } from './DeleteConfirmDialog';

interface NoteCardProps {
  note: Note;
  onDelete?: (id: number) => void;
}

export function NoteCard({ note, onDelete }: NoteCardProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const handleDelete = () => {
    onDelete?.(note.id);
    setShowDeleteDialog(false);
  };

  return (
    <>
      <div className="retro-card group">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1 min-w-0">
            <h3 className="retro-heading text-xl mb-2 truncate">
              {note.title}
            </h3>
            <div className="flex items-center space-x-4 text-sm opacity-75">
              <div className="flex items-center space-x-1">
                <Calendar className="w-4 h-4" />
                <span className="retro-text">
                  {formatRelativeTime(note.created_at)}
                </span>
              </div>
              {note.updated_at !== note.created_at && (
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span className="retro-text">
                    Updated {formatRelativeTime(note.updated_at)}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <Link
              href={`/notes/${note.id}`}
              className={cn(
                'p-2 rounded-lg transition-all duration-200',
                'bg-retro-green text-white hover:bg-retro-green-dark',
                'hover:shadow-retro hover:-translate-y-0.5'
              )}
              title="View note"
            >
              <Eye className="w-4 h-4" />
            </Link>
            
            <Link
              href={`/notes/${note.id}/edit`}
              className={cn(
                'p-2 rounded-lg transition-all duration-200',
                'bg-retro-orange text-white hover:bg-retro-orange-dark',
                'hover:shadow-retro hover:-translate-y-0.5'
              )}
              title="Edit note"
            >
              <Edit className="w-4 h-4" />
            </Link>
            
            <button
              onClick={() => setShowDeleteDialog(true)}
              className={cn(
                'p-2 rounded-lg transition-all duration-200',
                'bg-retro-red text-white hover:bg-red-600',
                'hover:shadow-retro hover:-translate-y-0.5'
              )}
              title="Delete note"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Content Preview */}
        <div className="mb-4">
          <p className="retro-text leading-relaxed">
            {getPreviewText(note.content, 200) || (
              <span className="italic opacity-60">No content</span>
            )}
          </p>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between pt-4 border-t border-retro-brown-light">
          <div className="flex items-center space-x-2">
            <span className="px-3 py-1 bg-retro-orange/20 text-retro-orange-dark rounded-full text-xs font-mono font-semibold">
              #{note.id}
            </span>
            <span className="retro-text text-xs">
              {note.content.length} characters
            </span>
          </div>

          <Link
            href={`/notes/${note.id}`}
            className="retro-text text-sm font-semibold hover:text-retro-orange transition-colors"
          >
            Read more →
          </Link>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmDialog
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        onConfirm={handleDelete}
        noteTitle={note.title}
      />
    </>
  );
}
