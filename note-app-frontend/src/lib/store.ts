import { create } from 'zustand';
import { Note, CreateNoteRequest, UpdateNoteRequest, SearchFilters, Theme } from './types';
import { apiClient } from './api';

interface NotesState {
  // Data
  notes: Note[];
  currentNote: Note | null;
  
  // UI State
  isLoading: boolean;
  error: string | null;
  searchFilters: SearchFilters;
  theme: Theme;
  
  // Actions
  fetchNotes: () => Promise<void>;
  fetchNote: (id: number) => Promise<void>;
  createNote: (data: CreateNoteRequest) => Promise<Note>;
  updateNote: (id: number, data: UpdateNoteRequest) => Promise<Note>;
  deleteNote: (id: number) => Promise<void>;
  
  // UI Actions
  setError: (error: string | null) => void;
  setSearchFilters: (filters: Partial<SearchFilters>) => void;
  toggleTheme: () => void;
  clearCurrentNote: () => void;
}

export const useNotesStore = create<NotesState>((set, get) => ({
  // Initial state
  notes: [],
  currentNote: null,
  isLoading: false,
  error: null,
  searchFilters: {
    query: '',
    sortBy: 'updated_at',
    sortOrder: 'desc',
  },
  theme: {
    isDark: false,
  },

  // Data actions
  fetchNotes: async () => {
    set({ isLoading: true, error: null });
    try {
      const notes = await apiClient.getNotes();
      set({ notes, isLoading: false });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch notes',
        isLoading: false 
      });
    }
  },

  fetchNote: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      const note = await apiClient.getNote(id);
      set({ currentNote: note, isLoading: false });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch note',
        isLoading: false 
      });
    }
  },

  createNote: async (data: CreateNoteRequest) => {
    set({ isLoading: true, error: null });
    try {
      const newNote = await apiClient.createNote(data);
      set(state => ({ 
        notes: [newNote, ...state.notes],
        isLoading: false 
      }));
      return newNote;
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to create note',
        isLoading: false 
      });
      throw error;
    }
  },

  updateNote: async (id: number, data: UpdateNoteRequest) => {
    set({ isLoading: true, error: null });
    try {
      const updatedNote = await apiClient.updateNote(id, data);
      set(state => ({
        notes: state.notes.map(note => 
          note.id === id ? updatedNote : note
        ),
        currentNote: state.currentNote?.id === id ? updatedNote : state.currentNote,
        isLoading: false
      }));
      return updatedNote;
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to update note',
        isLoading: false 
      });
      throw error;
    }
  },

  deleteNote: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      await apiClient.deleteNote(id);
      set(state => ({
        notes: state.notes.filter(note => note.id !== id),
        currentNote: state.currentNote?.id === id ? null : state.currentNote,
        isLoading: false
      }));
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to delete note',
        isLoading: false 
      });
      throw error;
    }
  },

  // UI actions
  setError: (error: string | null) => set({ error }),

  setSearchFilters: (filters: Partial<SearchFilters>) => 
    set(state => ({ 
      searchFilters: { ...state.searchFilters, ...filters } 
    })),

  toggleTheme: () => 
    set(state => ({ 
      theme: { isDark: !state.theme.isDark } 
    })),

  clearCurrentNote: () => set({ currentNote: null }),
}));
