export interface Note {
  id: number;
  title: string;
  content: string;
  created_at: string;
  updated_at: string;
}

export interface CreateNoteRequest {
  title: string;
  content: string;
}

export interface UpdateNoteRequest {
  title?: string;
  content?: string;
}

export interface ApiError {
  error: string;
}

export interface DeleteNoteResponse {
  message: string;
}

export interface SearchFilters {
  query: string;
  sortBy: 'created_at' | 'updated_at' | 'title';
  sortOrder: 'asc' | 'desc';
}

export interface Theme {
  isDark: boolean;
}
