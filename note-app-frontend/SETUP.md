# Retro Notes Frontend Setup Guide

## Quick Start

### Prerequisites
- Node.js 18+ installed
- npm or yarn package manager
- Flask backend running on http://localhost:5000

### Installation Steps

1. **Navigate to the frontend directory:**
   ```bash
   cd note-app-frontend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Start the development server:**
   ```bash
   npm run dev
   ```
   
   Or use the convenience scripts:
   - Windows: `start-dev.bat`
   - Linux/Mac: `./start-dev.sh`

4. **Open your browser:**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Flask Backend Setup

Make sure your Flask backend is running with these endpoints:

- `GET /notes` - Get all notes
- `POST /notes` - Create a new note
- `GET /notes/:id` - Get a specific note  
- `PUT /notes/:id` - Update a note
- `DELETE /notes/:id` - Delete a note

### Starting the Flask Backend

1. Navigate to the backend directory:
   ```bash
   cd ../note-app-be
   ```

2. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Start the Flask server:
   ```bash
   python app.py
   ```

The Flask backend should be running on http://localhost:5000

## Environment Configuration

The frontend is configured to connect to the Flask backend at `http://localhost:5000` by default.

To change this, edit the `.env.local` file:
```
NEXT_PUBLIC_API_URL=http://your-backend-url:port
```

## Features Overview

### Core Functionality
✅ **Complete CRUD Operations**
- Create new notes with title and content
- View all notes in a responsive grid layout
- Read individual notes with full content display
- Edit existing notes with pre-populated forms
- Delete notes with confirmation dialogs

✅ **Search & Filtering**
- Real-time search across note titles and content
- Sort by title, creation date, or last updated
- Ascending/descending sort options
- Filter status display and quick clear options

✅ **User Experience**
- Loading states for all operations
- Error handling with user-friendly messages
- Responsive design for mobile and desktop
- Keyboard shortcuts (Ctrl+S to save, Escape to cancel)
- Auto-save functionality (draft implementation)

### Design Features
✅ **Retro/Vintage Aesthetic**
- Warm color palette (oranges, browns, creams, muted greens)
- Vintage typography with Google Fonts (Courier Prime, Crimson Text)
- Paper-like textures and backgrounds
- Retro UI elements with rounded corners and drop shadows
- Custom animations (typewriter effect, loading spinners)

✅ **Theme System**
- Dark/light theme toggle
- Both themes maintain retro styling
- Smooth transitions between themes
- Persistent theme preference

✅ **Interactive Elements**
- Hover effects on cards and buttons
- Focus states for accessibility
- Custom scrollbars matching the retro theme
- Smooth animations and transitions

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── layout.tsx         # Root layout with theme and error boundary
│   ├── page.tsx           # Home page (notes listing)
│   └── notes/
│       ├── create/        # Create note page
│       └── [id]/          # Dynamic note routes
│           ├── page.tsx   # Note detail view
│           └── edit/      # Edit note page
├── components/            # Reusable components
│   ├── Layout/           # Header and navigation
│   ├── NoteCard.tsx      # Individual note display
│   ├── NoteForm.tsx      # Create/edit form
│   ├── SearchBar.tsx     # Search and filtering
│   ├── DeleteConfirmDialog.tsx  # Delete confirmation
│   ├── LoadingSpinner.tsx       # Loading states
│   ├── ErrorBoundary.tsx        # Error handling
│   └── ThemeProvider.tsx        # Theme management
├── lib/                  # Utilities and services
│   ├── api.ts           # API client for Flask backend
│   ├── store.ts         # Zustand state management
│   ├── types.ts         # TypeScript interfaces
│   └── utils.ts         # Helper functions
└── styles/              # Global styles and theme
    └── globals.css      # Retro theme and component styles
```

## API Integration

The frontend uses a custom API client (`src/lib/api.ts`) that:
- Handles all HTTP requests to the Flask backend
- Provides proper error handling and user feedback
- Includes TypeScript types for request/response data
- Manages JSON serialization/deserialization
- Supports configurable base URL

## State Management

Uses Zustand for lightweight, efficient state management:
- **Notes Data**: Current notes list and individual note state
- **UI State**: Loading indicators, error messages, search filters
- **Theme State**: Dark/light mode preference
- **Async Actions**: API calls with proper error handling

## Styling System

Built with Tailwind CSS and custom retro theme:
- **Color System**: Comprehensive retro color palette
- **Typography**: Vintage font combinations
- **Components**: Reusable retro-styled components
- **Animations**: Custom keyframe animations
- **Responsive**: Mobile-first responsive design
- **Accessibility**: Proper focus states and ARIA labels

## Development Commands

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm run start

# Run linting
npm run lint
```

## Browser Support

- Chrome/Edge 88+
- Firefox 85+
- Safari 14+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Troubleshooting

### Common Issues

1. **Backend Connection Error**
   - Ensure Flask backend is running on http://localhost:5000
   - Check that all Flask endpoints are working
   - Verify CORS is properly configured in Flask

2. **Build Errors**
   - Run `npm install` to ensure all dependencies are installed
   - Check for TypeScript errors in the console
   - Ensure all imports are correct

3. **Styling Issues**
   - Verify Tailwind CSS is properly configured
   - Check that custom CSS classes are defined in globals.css
   - Ensure fonts are loading correctly

4. **State Management Issues**
   - Check browser console for Zustand errors
   - Verify API responses match expected TypeScript types
   - Ensure proper error handling in async actions

### Getting Help

If you encounter issues:
1. Check the browser console for error messages
2. Verify the Flask backend is responding correctly
3. Review the network tab for failed API requests
4. Check the project's README.md for additional information

## Next Steps

After setup, you can:
1. Create your first note using the "Create New Note" button
2. Explore the search and filtering features
3. Try the dark/light theme toggle
4. Test the responsive design on different screen sizes
5. Use keyboard shortcuts for efficient note editing

Enjoy your vintage note-taking experience! 📝✨
