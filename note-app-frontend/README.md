# Retro Notes Frontend

A vintage-styled note-taking application built with Next.js 15, featuring a retro aesthetic and modern functionality.

## Features

### Core Functionality
- ✅ Complete CRUD operations for notes (Create, Read, Update, Delete)
- ✅ Note listing with title, content preview, and timestamps
- ✅ Detailed note view with full content display
- ✅ Note creation and editing forms
- ✅ Delete confirmation dialogs
- ✅ Search and filtering capabilities
- ✅ Responsive design for mobile and desktop

### Technical Features
- ✅ Next.js 15 with App Router architecture
- ✅ TypeScript for type safety
- ✅ Zustand for state management
- ✅ Tailwind CSS with custom retro theme
- ✅ Error handling and loading states
- ✅ API integration with Flask backend
- ✅ Auto-save functionality (draft implementation)
- ✅ Keyboard shortcuts (Ctrl+S to save, Escape to cancel)

### Design Features
- ✅ Retro/vintage aesthetic with warm color palette
- ✅ Vintage typography (Courier Prime, Crimson Text)
- ✅ Paper-like textures and backgrounds
- ✅ Retro UI elements (rounded corners, drop shadows, vintage buttons)
- ✅ Dark/light theme toggle (both in retro style)
- ✅ Typewriter animations and retro effects
- ✅ Custom scrollbars and focus states

## Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Flask backend running on http://localhost:5000

### Installation

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
cp .env.local.example .env.local
# Edit .env.local with your Flask backend URL
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

### Flask Backend Setup
Make sure your Flask backend is running on http://localhost:5000 with the following endpoints:
- `GET /notes` - Get all notes
- `POST /notes` - Create a new note
- `GET /notes/:id` - Get a specific note
- `PUT /notes/:id` - Update a note
- `DELETE /notes/:id` - Delete a note

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── layout.tsx         # Root layout
│   ├── page.tsx           # Home page (notes listing)
│   └── notes/
│       ├── create/        # Create note page
│       └── [id]/          # Dynamic note routes
├── components/            # Reusable components
│   ├── Layout/           # Layout components
│   ├── NoteCard.tsx      # Individual note display
│   ├── NoteForm.tsx      # Create/edit form
│   ├── SearchBar.tsx     # Search and filtering
│   └── ...
├── lib/                  # Utilities and services
│   ├── api.ts           # API client
│   ├── store.ts         # Zustand store
│   ├── types.ts         # TypeScript types
│   └── utils.ts         # Helper functions
└── styles/              # Global styles
```

## API Integration

The frontend connects to the Flask backend using a custom API client (`src/lib/api.ts`) that handles:
- HTTP requests with proper error handling
- Request/response type safety
- Base URL configuration
- JSON serialization/deserialization

## State Management

Uses Zustand for lightweight state management with:
- Notes data and current note state
- Loading and error states
- Search filters and UI preferences
- Theme management
- Async actions for API calls

## Styling

Custom retro theme built with Tailwind CSS featuring:
- Warm color palette (oranges, browns, creams, muted greens)
- Vintage typography with Google Fonts
- Paper textures and retro shadows
- Custom animations (typewriter, spinner)
- Dark/light theme variants
- Responsive design utilities

## Keyboard Shortcuts

- `Ctrl+S` / `Cmd+S` - Save note (in create/edit forms)
- `Escape` - Cancel editing
- Standard navigation shortcuts

## Browser Support

- Chrome/Edge 88+
- Firefox 85+
- Safari 14+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Environment Variables

- `NEXT_PUBLIC_API_URL` - Flask backend URL (default: http://localhost:5000)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
