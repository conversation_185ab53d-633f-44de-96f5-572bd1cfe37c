#!/usr/bin/env node

const http = require('http');

console.log('🧪 Testing Retro Notes API...\n');

// Test configuration
const API_BASE = 'http://localhost:5000';
const tests = [
  {
    name: 'Get All Notes',
    method: 'GET',
    path: '/notes',
    expectedStatus: 200
  },
  {
    name: 'Create Note',
    method: 'POST',
    path: '/notes',
    data: JSON.stringify({
      title: 'Test Note',
      content: 'This is a test note created by the API test script.'
    }),
    expectedStatus: 201
  }
];

// Simple HTTP request function
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: body
        });
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(data);
    }
    
    req.end();
  });
}

// Run tests
async function runTests() {
  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    try {
      console.log(`🔍 Testing: ${test.name}`);
      
      const options = {
        hostname: 'localhost',
        port: 5000,
        path: test.path,
        method: test.method,
        headers: {
          'Content-Type': 'application/json'
        }
      };

      const response = await makeRequest(options, test.data);
      
      if (response.statusCode === test.expectedStatus) {
        console.log(`✅ PASS: ${test.name} (${response.statusCode})`);
        passed++;
      } else {
        console.log(`❌ FAIL: ${test.name} (Expected: ${test.expectedStatus}, Got: ${response.statusCode})`);
        failed++;
      }
      
      // Show response body for debugging
      if (response.body) {
        try {
          const parsed = JSON.parse(response.body);
          console.log(`   Response: ${JSON.stringify(parsed, null, 2).substring(0, 200)}...`);
        } catch (e) {
          console.log(`   Response: ${response.body.substring(0, 200)}...`);
        }
      }
      
    } catch (error) {
      console.log(`❌ ERROR: ${test.name} - ${error.message}`);
      failed++;
    }
    
    console.log(''); // Empty line for readability
  }

  // Summary
  console.log('📊 Test Summary:');
  console.log(`   ✅ Passed: ${passed}`);
  console.log(`   ❌ Failed: ${failed}`);
  console.log(`   📈 Total:  ${passed + failed}`);

  if (failed === 0) {
    console.log('\n🎉 All tests passed! Your API is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Check your Flask backend server.');
    console.log('   Make sure it\'s running on http://localhost:5000');
  }
}

// Check if backend is running first
console.log('🔍 Checking if Flask backend is running...');
makeRequest({
  hostname: 'localhost',
  port: 5000,
  path: '/notes',
  method: 'GET'
}).then(() => {
  console.log('✅ Backend is responding\n');
  runTests();
}).catch((error) => {
  console.log('❌ Backend is not responding');
  console.log('   Please start the Flask backend server first:');
  console.log('   1. Run: npm run start');
  console.log('   2. Or manually: cd note-app-be && python app.py');
  console.log(`   Error: ${error.message}`);
});
