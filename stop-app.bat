@echo off
echo.
echo ========================================
echo    🛑 RETRO NOTES - SHUTDOWN SCRIPT 🛑
echo ========================================
echo.

echo 🔍 Looking for running servers...

:: Kill processes on port 5000 (Flask backend)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :5000') do (
    echo Stopping Flask backend server (PID: %%a)...
    taskkill /PID %%a /F >nul 2>&1
)

:: Kill processes on port 3000 (Next.js frontend)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3000') do (
    echo Stopping Next.js frontend server (PID: %%a)...
    taskkill /PID %%a /F >nul 2>&1
)

:: Close any command windows with our titles
taskkill /FI "WINDOWTITLE eq Flask Backend - Retro Notes" /F >nul 2>&1
taskkill /FI "WINDOWTITLE eq Next.js Frontend - Retro Notes" /F >nul 2>&1

echo.
echo ✅ All Retro Notes servers have been stopped
echo.
pause
